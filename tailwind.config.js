import { heroui } from "@heroui/theme";

/** @type {import('tailwindcss').Config} */
const config = {
  content: [
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "./node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ["var(--font-sans)"],
        mono: ["var(--font-mono)"],
      },
      colors: {
        // Enhanced slate colors for professional dark theme
        slate: {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#64748b',
          600: '#475569',
          700: '#334155',
          800: '#1e293b',
          900: '#0f172a',
          950: '#020617',
        },
        // Softer text colors for better readability
        'soft-black': '#1f2937', // gray-800 equivalent
        'soft-dark': '#374151',  // gray-700 equivalent
        'soft-medium': '#4b5563', // gray-600 equivalent
        'soft-light': '#6b7280',  // gray-500 equivalent
      }
    },
  },
  darkMode: "class",
  plugins: [
    heroui({
      themes: {
        light: {
          colors: {
            background: "#f8fafc", // slate-50
            foreground: "#0f172a", // slate-900
            content1: "#ffffff", // white
            content2: "#f8fafc", // slate-50
            content3: "#f1f5f9", // slate-100
            content4: "#e2e8f0", // slate-200
            default: {
              50: "#f8fafc",
              100: "#f1f5f9",
              200: "#e2e8f0",
              300: "#cbd5e1",
              400: "#94a3b8",
              500: "#64748b",
              600: "#475569",
              700: "#334155",
              800: "#1e293b",
              900: "#0f172a",
              DEFAULT: "#f1f5f9",
              foreground: "#0f172a",
            },
          },
        },
        dark: {
          colors: {
            background: "#0f172a", // slate-900
            foreground: "#f8fafc", // slate-50
            content1: "#1e293b", // slate-800 - for table backgrounds
            content2: "#334155", // slate-700 - for hover states
            content3: "#475569", // slate-600 - for borders
            content4: "#64748b", // slate-500 - for dividers
            default: {
              50: "#0f172a",
              100: "#1e293b",
              200: "#334155",
              300: "#475569",
              400: "#64748b",
              500: "#94a3b8",
              600: "#cbd5e1",
              700: "#e2e8f0",
              800: "#f1f5f9",
              900: "#f8fafc",
              DEFAULT: "#1e293b",
              foreground: "#f8fafc",
            },
          },
        },
      },
    }),
  ],
};

export default config;
