import { heroui } from "@heroui/theme";

/** @type {import('tailwindcss').Config} */
const config = {
  content: [
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "./node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ["var(--font-sans)"],
        mono: ["var(--font-mono)"],
      },
      colors: {
        // Enhanced slate colors for professional dark theme
        slate: {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#64748b',
          600: '#475569',
          700: '#334155',
          800: '#1e293b',
          900: '#0f172a',
          950: '#020617',
        },
        // Softer text colors for better readability
        'soft-black': '#1f2937', // gray-800 equivalent
        'soft-dark': '#374151',  // gray-700 equivalent
        'soft-medium': '#4b5563', // gray-600 equivalent
        'soft-light': '#6b7280',  // gray-500 equivalent
      }
    },
  },
  darkMode: "class",
  plugins: [
    heroui({
      themes: {
        light: {
          colors: {
            background: "#f8fafc", // slate-50 for consistency
          },
        },
        dark: {
          colors: {
            background: "#0f172a", // slate-900 for consistency
          },
        },
      },
    }),
  ],
};

export default config;
