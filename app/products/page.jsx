import { redirect } from 'next/navigation';
import { getCurrentUserServer } from '@/utils/server-auth';
import ProductsClient from '@/components/products/ProductsClient';

/**
 * Products Page (Server Component)
 *
 * Protected products page with server-side authentication check.
 * Redirects to login if user is not authenticated.
 */
export default async function ProductsPage() {
  // Check authentication on server-side
  const user = await getCurrentUserServer();

  // If not authenticated, redirect to login
  if (!user) {
    redirect('/');
  }

  // Show products page for authenticated users
  return <ProductsClient user={user} />;
}
