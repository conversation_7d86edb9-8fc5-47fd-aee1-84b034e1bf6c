import { redirect } from 'next/navigation';
import { getCurrentUserServer } from '@/utils/server-auth';
import CustomersClient from '@/components/customers/CustomersClient';

/**
 * Customers Page (Server Component)
 *
 * Protected customers page with server-side authentication check.
 * Redirects to login if user is not authenticated.
 */
export default async function CustomersPage() {
  // Check authentication on server-side
  const user = await getCurrentUserServer();

  // If not authenticated, redirect to login
  if (!user) {
    redirect('/');
  }

  // Show customers page for authenticated users
  return <CustomersClient user={user} />;
}
