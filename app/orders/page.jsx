import { redirect } from 'next/navigation';
import { getCurrentUserServer } from '@/utils/server-auth';
import OrdersClient from '@/components/orders/OrdersClient';

/**
 * Orders Page (Server Component)
 *
 * Protected orders page with server-side authentication check.
 * Redirects to login if user is not authenticated.
 */
export default async function OrdersPage() {
  // Check authentication on server-side
  const user = await getCurrentUserServer();

  // If not authenticated, redirect to login
  if (!user) {
    redirect('/');
  }

  // Show orders page for authenticated users
  return <OrdersClient user={user} />;
}
