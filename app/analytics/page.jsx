import { redirect } from 'next/navigation';
import { getCurrentUserServer } from '@/utils/server-auth';
import AnalyticsClient from '@/components/analytics/AnalyticsClient';

/**
 * Analytics Page (Server Component)
 *
 * Protected analytics page with server-side authentication check.
 * Redirects to login if user is not authenticated.
 */
export default async function AnalyticsPage() {
  // Check authentication on server-side
  const user = await getCurrentUserServer();

  // If not authenticated, redirect to login
  if (!user) {
    redirect('/');
  }

  // Show analytics page for authenticated users
  return <AnalyticsClient user={user} />;
}
