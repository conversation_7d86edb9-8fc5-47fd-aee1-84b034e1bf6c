import { redirect } from 'next/navigation';
import { getCurrentUserServer } from '@/utils/server-auth';
import SettingsClient from '@/components/settings/SettingsClient';

/**
 * Settings Page (Server Component)
 *
 * Protected settings page with server-side authentication check.
 * Redirects to login if user is not authenticated.
 */
export default async function SettingsPage() {
  // Check authentication on server-side
  const user = await getCurrentUserServer();

  // If not authenticated, redirect to login
  if (!user) {
    redirect('/');
  }

  // Show settings page for authenticated users
  return <SettingsClient user={user} />;
}
