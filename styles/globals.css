@tailwind base;
@tailwind components;
@tailwind utilities;

/* Override HeroUI CSS variables for consistent background colors */
@layer base {
  :root {
    /* Try multiple possible variable names */
    --heroui-background: 248 250 252; /* slate-50 RGB */
    --heroui-colors-background: 248 250 252;
    --nextui-background: 248 250 252;
    --nextui-colors-background: 248 250 252;
    --heroui-foreground: 15 23 42; /* slate-900 RGB */
  }

  .dark {
    /* Dark theme background - try multiple variable names */
    --heroui-background: 15 23 42; /* slate-900 RGB */
    --heroui-colors-background: 15 23 42;
    --nextui-background: 15 23 42;
    --nextui-colors-background: 15 23 42;
    --heroui-foreground: 248 250 252; /* slate-50 RGB */
  }

  /* Force background color directly */
  html, body {
    background-color: rgb(248 250 252) !important;
  }

  .dark html, .dark body {
    background-color: rgb(15 23 42) !important;
  }

  /* Override any component backgrounds */
  .dark * {
    --tw-bg-opacity: 1;
  }

  /* Force override bg-background class */
  .bg-background {
    background-color: rgb(248 250 252) !important;
  }

  .dark .bg-background {
    background-color: rgb(15 23 42) !important;
  }

  /* Target specific elements that might be using black */
  .dark [data-theme="dark"],
  .dark [class*="heroui"],
  .dark [class*="nextui"] {
    background-color: rgb(15 23 42) !important;
  }

  /* Nuclear option - override everything */
  .dark,
  .dark *,
  .dark body,
  .dark html,
  .dark main,
  .dark div[class*="min-h-screen"] {
    background-color: rgb(15 23 42) !important;
  }

  /* Make sure cards and components don't override */
  .dark .bg-black {
    background-color: rgb(15 23 42) !important;
  }
}
}

/* Custom color improvements for better readability */
@layer utilities {
  /* Softer text colors for light mode - replace harsh black with dark gray */
  .text-soft-black {
    @apply text-gray-800 dark:text-white;
  }

  .text-soft-dark {
    @apply text-gray-700 dark:text-gray-200;
  }

  .text-soft-medium {
    @apply text-gray-600 dark:text-gray-300;
  }

  .text-soft-light {
    @apply text-gray-500 dark:text-gray-400;
  }

  /* Override harsh gray-900 with softer gray-800 */
  .text-gray-900 {
    @apply text-gray-800 dark:text-white !important;
  }

  /* Custom scrollbar styles for better sidebar scrolling */
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
    background-color: rgb(209 213 219);
    border-radius: 0.375rem;
  }

  .dark .scrollbar-thumb-slate-600::-webkit-scrollbar-thumb {
    background-color: rgb(71 85 105);
    border-radius: 0.375rem;
  }

  .scrollbar-track-transparent::-webkit-scrollbar-track {
    background-color: transparent;
  }

  .hover\:scrollbar-thumb-gray-400:hover::-webkit-scrollbar-thumb {
    background-color: rgb(156 163 175);
  }

  .dark .hover\:scrollbar-thumb-slate-500:hover::-webkit-scrollbar-thumb {
    background-color: rgb(100 116 139);
  }

  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: rgb(209 213 219);
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgb(156 163 175);
  }

  .dark ::-webkit-scrollbar-thumb {
    background: rgb(71 85 105);
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    background: rgb(100 116 139);
  }
}
