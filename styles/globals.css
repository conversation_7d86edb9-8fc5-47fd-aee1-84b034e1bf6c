@tailwind base;
@tailwind components;
@tailwind utilities;

/* Ensure html and body use the background color */
@layer base {
  html, body {
    @apply bg-background;
  }
}

/* Custom color improvements for better readability */
@layer utilities {
  /* Softer text colors for light mode - replace harsh black with dark gray */
  .text-soft-black {
    @apply text-gray-800 dark:text-white;
  }

  .text-soft-dark {
    @apply text-gray-700 dark:text-gray-200;
  }

  .text-soft-medium {
    @apply text-gray-600 dark:text-gray-300;
  }

  .text-soft-light {
    @apply text-gray-500 dark:text-gray-400;
  }

  /* Override harsh gray-900 with softer gray-800 */
  .text-gray-900 {
    @apply text-gray-800 dark:text-white !important;
  }

  /* Force input fields to have proper contrast */
  [data-slot="input-wrapper"],
  [data-slot="innerWrapper"],
  .heroui-input-wrapper {
    @apply bg-slate-700 dark:bg-slate-700 !important;
  }

  [data-slot="input"],
  input[data-slot="input"],
  .heroui-input {
    @apply bg-slate-700 dark:bg-slate-700 text-white placeholder:text-slate-400 !important;
  }

  /* Input hover and focus states */
  [data-slot="input-wrapper"]:hover,
  .heroui-input-wrapper:hover {
    @apply bg-slate-600 dark:bg-slate-600 !important;
  }

  [data-slot="input-wrapper"][data-focus="true"],
  .heroui-input-wrapper[data-focus="true"] {
    @apply bg-slate-600 dark:bg-slate-600 !important;
  }

  /* Custom scrollbar styles for better sidebar scrolling */
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
    background-color: rgb(209 213 219);
    border-radius: 0.375rem;
  }

  .dark .scrollbar-thumb-slate-600::-webkit-scrollbar-thumb {
    background-color: rgb(71 85 105);
    border-radius: 0.375rem;
  }

  .scrollbar-track-transparent::-webkit-scrollbar-track {
    background-color: transparent;
  }

  .hover\:scrollbar-thumb-gray-400:hover::-webkit-scrollbar-thumb {
    background-color: rgb(156 163 175);
  }

  .dark .hover\:scrollbar-thumb-slate-500:hover::-webkit-scrollbar-thumb {
    background-color: rgb(100 116 139);
  }

  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: rgb(209 213 219);
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgb(156 163 175);
  }

  .dark ::-webkit-scrollbar-thumb {
    background: rgb(71 85 105);
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    background: rgb(100 116 139);
  }
}
