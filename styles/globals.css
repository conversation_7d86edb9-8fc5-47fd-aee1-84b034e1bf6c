@tailwind base;
@tailwind components;
@tailwind utilities;

/* Override HeroUI CSS variables for consistent background colors */
@layer base {
  :root {
    /* Light theme - HSL values for slate-50 */
    --heroui-background: 210 40% 98%; /* slate-50 HSL */
    --heroui-foreground: 222 84% 5%; /* slate-900 HSL */
  }

  .dark {
    /* Dark theme - HSL values for slate-900 */
    --heroui-background: 222 84% 5%; /* slate-900 HSL */
    --heroui-foreground: 210 40% 98%; /* slate-50 HSL */

    /* Override table and content specific variables */
    --heroui-content1: 222 84% 5%; /* slate-900 HSL */
    --heroui-content2: 222 47% 11%; /* slate-800 HSL */
    --heroui-content3: 215 28% 17%; /* slate-700 HSL */
    --heroui-content4: 215 20% 25%; /* slate-600 HSL */

    /* Override default colors that might be used by tables */
    --heroui-default: 222 84% 5%; /* slate-900 HSL */
    --heroui-default-50: 222 84% 5%;
    --heroui-default-100: 222 47% 11%;
    --heroui-default-200: 215 28% 17%;
  }

  /* Ensure html and body use the background color */
  html, body {
    @apply bg-background;
  }
}

/* Custom color improvements for better readability */
@layer utilities {
  /* Softer text colors for light mode - replace harsh black with dark gray */
  .text-soft-black {
    @apply text-gray-800 dark:text-white;
  }

  .text-soft-dark {
    @apply text-gray-700 dark:text-gray-200;
  }

  .text-soft-medium {
    @apply text-gray-600 dark:text-gray-300;
  }

  .text-soft-light {
    @apply text-gray-500 dark:text-gray-400;
  }

  /* Override harsh gray-900 with softer gray-800 */
  .text-gray-900 {
    @apply text-gray-800 dark:text-white !important;
  }

  /* Force table backgrounds to use slate colors instead of black */
  [data-slot="table"],
  [data-slot="tbody"],
  [data-slot="thead"],
  .table-wrapper,
  table {
    @apply bg-slate-900 dark:bg-slate-900 !important;
  }

  [data-slot="tr"],
  tr {
    @apply bg-slate-900 dark:bg-slate-900 hover:bg-slate-800 dark:hover:bg-slate-800 !important;
  }

  [data-slot="td"],
  [data-slot="th"],
  td,
  th {
    @apply bg-transparent !important;
  }

  /* Custom scrollbar styles for better sidebar scrolling */
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
    background-color: rgb(209 213 219);
    border-radius: 0.375rem;
  }

  .dark .scrollbar-thumb-slate-600::-webkit-scrollbar-thumb {
    background-color: rgb(71 85 105);
    border-radius: 0.375rem;
  }

  .scrollbar-track-transparent::-webkit-scrollbar-track {
    background-color: transparent;
  }

  .hover\:scrollbar-thumb-gray-400:hover::-webkit-scrollbar-thumb {
    background-color: rgb(156 163 175);
  }

  .dark .hover\:scrollbar-thumb-slate-500:hover::-webkit-scrollbar-thumb {
    background-color: rgb(100 116 139);
  }

  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: rgb(209 213 219);
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgb(156 163 175);
  }

  .dark ::-webkit-scrollbar-thumb {
    background: rgb(71 85 105);
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    background: rgb(100 116 139);
  }
}
