"use client";

import { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader } from '@heroui/card';
import { Button } from '@heroui/button';
import { Input } from '@heroui/input';
import { Chip } from '@heroui/chip';
import SidebarLayout from '@/components/layout/SidebarLayout';
import { 
  PlusIcon, 
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon
} from '@heroicons/react/24/outline';

/**
 * Products Client Component
 *
 * Client-side products management with interactive functionality.
 */
export default function ProductsClient({ user }) {
  const [mounted, setMounted] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(false);

  // Handle hydration
  useEffect(() => {
    setMounted(true);
    // TODO: Fetch products from API
    loadMockProducts();
  }, []);

  const loadMockProducts = () => {
    // Mock data for demonstration
    const mockProducts = [
      {
        id: 1,
        name: 'Premium Headphones',
        sku: 'HP-001',
        category: 'Electronics',
        price: 299.99,
        stock: 45,
        status: 'active',
        image: '/api/placeholder/100/100'
      },
      {
        id: 2,
        name: 'Wireless Mouse',
        sku: 'MS-002',
        category: 'Electronics',
        price: 49.99,
        stock: 120,
        status: 'active',
        image: '/api/placeholder/100/100'
      },
      {
        id: 3,
        name: 'Gaming Keyboard',
        sku: 'KB-003',
        category: 'Electronics',
        price: 159.99,
        stock: 0,
        status: 'out_of_stock',
        image: '/api/placeholder/100/100'
      }
    ];
    setProducts(mockProducts);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'success';
      case 'out_of_stock': return 'danger';
      case 'discontinued': return 'warning';
      default: return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'active': return 'Active';
      case 'out_of_stock': return 'Out of Stock';
      case 'discontinued': return 'Discontinued';
      default: return 'Unknown';
    }
  };

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <SidebarLayout user={user}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Products
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Manage your product inventory and catalog
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <Button
            color="primary"
            startContent={<PlusIcon className="h-5 w-5" />}
            className="font-medium"
          >
            Add Product
          </Button>
        </div>
      </div>

      {/* Filters and Search */}
      <Card className="mb-6 shadow-sm border-0">
        <CardBody className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search products by name, SKU, or category..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                startContent={<MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />}
                className="w-full"
              />
            </div>
            <Button
              variant="bordered"
              startContent={<FunnelIcon className="h-5 w-5" />}
              className="sm:w-auto"
            >
              Filters
            </Button>
          </div>
        </CardBody>
      </Card>

      {/* Products Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {filteredProducts.map((product) => (
          <Card key={product.id} className="hover:shadow-lg transition-all duration-200 border-0 shadow-sm">
            <CardBody className="p-0">
              {/* Product Image */}
              <div className="aspect-square bg-gray-100 dark:bg-gray-800 rounded-t-lg flex items-center justify-center">
                <div className="w-20 h-20 bg-gray-300 dark:bg-gray-600 rounded-lg flex items-center justify-center">
                  <span className="text-gray-500 dark:text-gray-400 text-sm">No Image</span>
                </div>
              </div>
              
              {/* Product Info */}
              <div className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-semibold text-gray-900 dark:text-white text-sm line-clamp-2">
                    {product.name}
                  </h3>
                  <Chip
                    size="sm"
                    color={getStatusColor(product.status)}
                    variant="flat"
                    className="ml-2 flex-shrink-0"
                  >
                    {getStatusText(product.status)}
                  </Chip>
                </div>
                
                <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                  SKU: {product.sku}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 mb-3">
                  Category: {product.category}
                </p>
                
                <div className="flex items-center justify-between mb-4">
                  <span className="text-lg font-bold text-gray-900 dark:text-white">
                    ${product.price}
                  </span>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    Stock: {product.stock}
                  </span>
                </div>
                
                {/* Actions */}
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="flat"
                    color="primary"
                    isIconOnly
                    className="flex-1"
                  >
                    <EyeIcon className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="flat"
                    color="warning"
                    isIconOnly
                    className="flex-1"
                  >
                    <PencilIcon className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="flat"
                    color="danger"
                    isIconOnly
                    className="flex-1"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardBody>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredProducts.length === 0 && (
        <Card className="mt-8 shadow-sm border-0">
          <CardBody className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 2L3 7v11a1 1 0 001 1h12a1 1 0 001-1V7l-7-5zM8 15a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No products found
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {searchTerm ? 'Try adjusting your search terms.' : 'Get started by adding your first product.'}
            </p>
            {!searchTerm && (
              <Button
                color="primary"
                startContent={<PlusIcon className="h-5 w-5" />}
              >
                Add Your First Product
              </Button>
            )}
          </CardBody>
        </Card>
      )}
    </SidebarLayout>
  );
}
