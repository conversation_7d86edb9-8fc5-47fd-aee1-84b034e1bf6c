"use client";

import { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader } from '@heroui/card';
import { Button } from '@heroui/button';
import { Select, SelectItem } from '@heroui/select';
import SidebarLayout from '@/components/layout/SidebarLayout';
import {
  ArrowUpIcon,
  ArrowDownIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';
import {
  DollarSign,
  ShoppingBag,
  Users,
  TrendingUp,
  BarChart3
} from 'lucide-react';

/**
 * Analytics Client Component
 *
 * Client-side analytics dashboard with interactive functionality.
 */
export default function AnalyticsClient({ user }) {
  const [mounted, setMounted] = useState(false);
  const [timeRange, setTimeRange] = useState('7d');
  const [analytics, setAnalytics] = useState({});

  // Handle hydration
  useEffect(() => {
    setMounted(true);
    loadMockAnalytics();
  }, [timeRange]);

  const loadMockAnalytics = () => {
    // Mock analytics data
    const mockData = {
      revenue: {
        current: 12450.00,
        previous: 10200.00,
        change: 22.1
      },
      orders: {
        current: 156,
        previous: 142,
        change: 9.9
      },
      customers: {
        current: 89,
        previous: 76,
        change: 17.1
      },
      avgOrderValue: {
        current: 79.81,
        previous: 71.83,
        change: 11.1
      },
      topProducts: [
        { name: 'Premium Headphones', sales: 45, revenue: 13495.50 },
        { name: 'Wireless Mouse', sales: 32, revenue: 1599.68 },
        { name: 'Gaming Keyboard', sales: 28, revenue: 4479.72 },
        { name: 'USB-C Cable', sales: 67, revenue: 1340.33 },
        { name: 'Phone Case', sales: 23, revenue: 689.77 }
      ],
      salesByDay: [
        { day: 'Mon', sales: 1200 },
        { day: 'Tue', sales: 1800 },
        { day: 'Wed', sales: 1600 },
        { day: 'Thu', sales: 2200 },
        { day: 'Fri', sales: 2800 },
        { day: 'Sat', sales: 1900 },
        { day: 'Sun', sales: 1450 }
      ]
    };
    setAnalytics(mockData);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatPercentage = (value) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  const getChangeColor = (change) => {
    return change > 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400';
  };

  const getChangeIcon = (change) => {
    return change > 0 ? ArrowUpIcon : ArrowDownIcon;
  };

  return (
    <SidebarLayout user={user}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Analytics
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Track your business performance and insights
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex gap-3">
          <Select
            placeholder="Select time range"
            selectedKeys={[timeRange]}
            onSelectionChange={(keys) => setTimeRange(Array.from(keys)[0])}
            className="w-40"
            startContent={<CalendarIcon className="h-4 w-4" />}
          >
            <SelectItem key="7d">Last 7 days</SelectItem>
            <SelectItem key="30d">Last 30 days</SelectItem>
            <SelectItem key="90d">Last 90 days</SelectItem>
            <SelectItem key="1y">Last year</SelectItem>
          </Select>
          <Button variant="bordered">
            Export Report
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* Revenue */}
        <Card className="shadow-sm border-0">
          <CardBody className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400">Revenue</h3>
              <div className="text-green-500 bg-green-50 dark:bg-green-900/20 p-2 rounded-lg">
                <DollarSign className="w-5 h-5" />
              </div>
            </div>
            <div className="flex items-end justify-between">
              <div>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {formatCurrency(analytics.revenue?.current || 0)}
                </p>
                <div className={`flex items-center mt-1 ${getChangeColor(analytics.revenue?.change || 0)}`}>
                  {analytics.revenue?.change && (() => {
                    const ChangeIcon = getChangeIcon(analytics.revenue.change);
                    return <ChangeIcon className="h-4 w-4 mr-1" />;
                  })()}
                  <span className="text-sm font-medium">
                    {formatPercentage(analytics.revenue?.change || 0)}
                  </span>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Orders */}
        <Card className="shadow-sm border-0">
          <CardBody className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400">Orders</h3>
              <div className="text-blue-500 bg-blue-50 dark:bg-blue-900/20 p-2 rounded-lg">
                <ShoppingBag className="w-5 h-5" />
              </div>
            </div>
            <div className="flex items-end justify-between">
              <div>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {analytics.orders?.current || 0}
                </p>
                <div className={`flex items-center mt-1 ${getChangeColor(analytics.orders?.change || 0)}`}>
                  {analytics.orders?.change && (() => {
                    const ChangeIcon = getChangeIcon(analytics.orders.change);
                    return <ChangeIcon className="h-4 w-4 mr-1" />;
                  })()}
                  <span className="text-sm font-medium">
                    {formatPercentage(analytics.orders?.change || 0)}
                  </span>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Customers */}
        <Card className="shadow-sm border-0">
          <CardBody className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400">New Customers</h3>
              <div className="text-purple-500 bg-purple-50 dark:bg-purple-900/20 p-2 rounded-lg">
                <Users className="w-5 h-5" />
              </div>
            </div>
            <div className="flex items-end justify-between">
              <div>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {analytics.customers?.current || 0}
                </p>
                <div className={`flex items-center mt-1 ${getChangeColor(analytics.customers?.change || 0)}`}>
                  {analytics.customers?.change && (() => {
                    const ChangeIcon = getChangeIcon(analytics.customers.change);
                    return <ChangeIcon className="h-4 w-4 mr-1" />;
                  })()}
                  <span className="text-sm font-medium">
                    {formatPercentage(analytics.customers?.change || 0)}
                  </span>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Average Order Value */}
        <Card className="shadow-sm border-0">
          <CardBody className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Order Value</h3>
              <div className="text-orange-500 bg-orange-50 dark:bg-orange-900/20 p-2 rounded-lg">
                <TrendingUp className="w-5 h-5" />
              </div>
            </div>
            <div className="flex items-end justify-between">
              <div>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {formatCurrency(analytics.avgOrderValue?.current || 0)}
                </p>
                <div className={`flex items-center mt-1 ${getChangeColor(analytics.avgOrderValue?.change || 0)}`}>
                  {analytics.avgOrderValue?.change && (() => {
                    const ChangeIcon = getChangeIcon(analytics.avgOrderValue.change);
                    return <ChangeIcon className="h-4 w-4 mr-1" />;
                  })()}
                  <span className="text-sm font-medium">
                    {formatPercentage(analytics.avgOrderValue?.change || 0)}
                  </span>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Charts and Tables */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sales Chart Placeholder */}
        <Card className="shadow-sm border-0">
          <CardHeader className="pb-3">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Sales Overview
            </h3>
          </CardHeader>
          <CardBody className="pt-0">
            <div className="h-64 bg-gray-50 dark:bg-gray-800 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <BarChart3 className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-500 dark:text-gray-400">Chart visualization coming soon</p>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Top Products */}
        <Card className="shadow-sm border-0">
          <CardHeader className="pb-3">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Top Products
            </h3>
          </CardHeader>
          <CardBody className="pt-0">
            <div className="space-y-4">
              {analytics.topProducts?.map((product, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="flex-1">
                    <p className="font-medium text-gray-900 dark:text-white text-sm">
                      {product.name}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {product.sales} sales
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-gray-900 dark:text-white text-sm">
                      {formatCurrency(product.revenue)}
                    </p>
                  </div>
                </div>
              )) || []}
            </div>
          </CardBody>
        </Card>
      </div>
    </SidebarLayout>
  );
}
