"use client";

import { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader } from '@heroui/card';
import { Button } from '@heroui/button';
import { Input } from '@heroui/input';
import { Switch } from '@heroui/switch';
import { Select, SelectItem } from '@heroui/select';
import { Textarea } from '@heroui/textarea';
import { Divider } from '@heroui/divider';
import SidebarLayout from '@/components/layout/SidebarLayout';
import { 
  UserIcon,
  BellIcon,
  ShieldCheckIcon,
  CogIcon,
  BuildingStorefrontIcon,
  CreditCardIcon
} from '@heroicons/react/24/outline';

/**
 * Settings Client Component
 *
 * Client-side settings management with interactive functionality.
 */
export default function SettingsClient({ user }) {
  const [mounted, setMounted] = useState(false);
  const [activeTab, setActiveTab] = useState('general');
  const [settings, setSettings] = useState({
    general: {
      siteName: 'Adimas Shop',
      siteDescription: 'Your premium e-commerce store',
      contactEmail: '<EMAIL>',
      timezone: 'UTC',
      language: 'en'
    },
    notifications: {
      emailNotifications: true,
      orderNotifications: true,
      inventoryAlerts: true,
      marketingEmails: false,
      weeklyReports: true
    },
    security: {
      twoFactorAuth: false,
      sessionTimeout: '30',
      passwordExpiry: '90',
      loginAttempts: '5'
    },
    store: {
      storeName: 'Adimas Shop',
      storeAddress: '123 Commerce St, Business City, BC 12345',
      currency: 'USD',
      taxRate: '8.5',
      shippingZones: 'domestic'
    }
  });

  // Handle hydration
  useEffect(() => {
    setMounted(true);
  }, []);

  const tabs = [
    { id: 'general', name: 'General', icon: CogIcon },
    { id: 'store', name: 'Store', icon: BuildingStorefrontIcon },
    { id: 'notifications', name: 'Notifications', icon: BellIcon },
    { id: 'security', name: 'Security', icon: ShieldCheckIcon },
    { id: 'profile', name: 'Profile', icon: UserIcon }
  ];

  const handleSettingChange = (category, key, value) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }));
  };

  const handleSave = () => {
    // TODO: Save settings to API
    console.log('Saving settings:', settings);
  };

  return (
    <SidebarLayout user={user}>
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          Settings
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Manage your application preferences and configuration
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar Navigation */}
        <div className="lg:col-span-1">
          <Card className="shadow-sm border-0">
            <CardBody className="p-0">
              <nav className="space-y-1">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`
                        w-full flex items-center px-4 py-3 text-sm font-medium rounded-none transition-colors
                        ${activeTab === tab.id
                          ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border-r-2 border-blue-500'
                          : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800'
                        }
                      `}
                    >
                      <Icon className="h-5 w-5 mr-3" />
                      {tab.name}
                    </button>
                  );
                })}
              </nav>
            </CardBody>
          </Card>
        </div>

        {/* Settings Content */}
        <div className="lg:col-span-3">
          <Card className="shadow-sm border-0">
            <CardBody className="p-6">
              {/* General Settings */}
              {activeTab === 'general' && (
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                    General Settings
                  </h2>
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <Input
                        label="Site Name"
                        value={settings.general.siteName}
                        onChange={(e) => handleSettingChange('general', 'siteName', e.target.value)}
                      />
                      <Input
                        label="Contact Email"
                        type="email"
                        value={settings.general.contactEmail}
                        onChange={(e) => handleSettingChange('general', 'contactEmail', e.target.value)}
                      />
                    </div>
                    <Textarea
                      label="Site Description"
                      value={settings.general.siteDescription}
                      onChange={(e) => handleSettingChange('general', 'siteDescription', e.target.value)}
                      rows={3}
                    />
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <Select
                        label="Timezone"
                        selectedKeys={[settings.general.timezone]}
                        onSelectionChange={(keys) => handleSettingChange('general', 'timezone', Array.from(keys)[0])}
                      >
                        <SelectItem key="UTC">UTC</SelectItem>
                        <SelectItem key="EST">Eastern Time</SelectItem>
                        <SelectItem key="PST">Pacific Time</SelectItem>
                        <SelectItem key="CST">Central Time</SelectItem>
                      </Select>
                      <Select
                        label="Language"
                        selectedKeys={[settings.general.language]}
                        onSelectionChange={(keys) => handleSettingChange('general', 'language', Array.from(keys)[0])}
                      >
                        <SelectItem key="en">English</SelectItem>
                        <SelectItem key="es">Spanish</SelectItem>
                        <SelectItem key="fr">French</SelectItem>
                        <SelectItem key="de">German</SelectItem>
                      </Select>
                    </div>
                  </div>
                </div>
              )}

              {/* Store Settings */}
              {activeTab === 'store' && (
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                    Store Settings
                  </h2>
                  <div className="space-y-6">
                    <Input
                      label="Store Name"
                      value={settings.store.storeName}
                      onChange={(e) => handleSettingChange('store', 'storeName', e.target.value)}
                    />
                    <Textarea
                      label="Store Address"
                      value={settings.store.storeAddress}
                      onChange={(e) => handleSettingChange('store', 'storeAddress', e.target.value)}
                      rows={3}
                    />
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <Select
                        label="Currency"
                        selectedKeys={[settings.store.currency]}
                        onSelectionChange={(keys) => handleSettingChange('store', 'currency', Array.from(keys)[0])}
                      >
                        <SelectItem key="USD">USD ($)</SelectItem>
                        <SelectItem key="EUR">EUR (€)</SelectItem>
                        <SelectItem key="GBP">GBP (£)</SelectItem>
                        <SelectItem key="JPY">JPY (¥)</SelectItem>
                      </Select>
                      <Input
                        label="Tax Rate (%)"
                        type="number"
                        value={settings.store.taxRate}
                        onChange={(e) => handleSettingChange('store', 'taxRate', e.target.value)}
                      />
                      <Select
                        label="Shipping Zones"
                        selectedKeys={[settings.store.shippingZones]}
                        onSelectionChange={(keys) => handleSettingChange('store', 'shippingZones', Array.from(keys)[0])}
                      >
                        <SelectItem key="domestic">Domestic Only</SelectItem>
                        <SelectItem key="international">International</SelectItem>
                        <SelectItem key="regional">Regional</SelectItem>
                      </Select>
                    </div>
                  </div>
                </div>
              )}

              {/* Notifications Settings */}
              {activeTab === 'notifications' && (
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                    Notification Settings
                  </h2>
                  <div className="space-y-6">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-sm font-medium text-gray-900 dark:text-white">Email Notifications</h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">Receive general email notifications</p>
                        </div>
                        <Switch
                          isSelected={settings.notifications.emailNotifications}
                          onValueChange={(value) => handleSettingChange('notifications', 'emailNotifications', value)}
                        />
                      </div>
                      <Divider />
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-sm font-medium text-gray-900 dark:text-white">Order Notifications</h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">Get notified about new orders</p>
                        </div>
                        <Switch
                          isSelected={settings.notifications.orderNotifications}
                          onValueChange={(value) => handleSettingChange('notifications', 'orderNotifications', value)}
                        />
                      </div>
                      <Divider />
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-sm font-medium text-gray-900 dark:text-white">Inventory Alerts</h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">Alerts when products are low in stock</p>
                        </div>
                        <Switch
                          isSelected={settings.notifications.inventoryAlerts}
                          onValueChange={(value) => handleSettingChange('notifications', 'inventoryAlerts', value)}
                        />
                      </div>
                      <Divider />
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-sm font-medium text-gray-900 dark:text-white">Marketing Emails</h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">Promotional and marketing content</p>
                        </div>
                        <Switch
                          isSelected={settings.notifications.marketingEmails}
                          onValueChange={(value) => handleSettingChange('notifications', 'marketingEmails', value)}
                        />
                      </div>
                      <Divider />
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-sm font-medium text-gray-900 dark:text-white">Weekly Reports</h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">Weekly business performance reports</p>
                        </div>
                        <Switch
                          isSelected={settings.notifications.weeklyReports}
                          onValueChange={(value) => handleSettingChange('notifications', 'weeklyReports', value)}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Security Settings */}
              {activeTab === 'security' && (
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                    Security Settings
                  </h2>
                  <div className="space-y-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-sm font-medium text-gray-900 dark:text-white">Two-Factor Authentication</h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400">Add an extra layer of security to your account</p>
                      </div>
                      <Switch
                        isSelected={settings.security.twoFactorAuth}
                        onValueChange={(value) => handleSettingChange('security', 'twoFactorAuth', value)}
                      />
                    </div>
                    <Divider />
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <Input
                        label="Session Timeout (minutes)"
                        type="number"
                        value={settings.security.sessionTimeout}
                        onChange={(e) => handleSettingChange('security', 'sessionTimeout', e.target.value)}
                      />
                      <Input
                        label="Password Expiry (days)"
                        type="number"
                        value={settings.security.passwordExpiry}
                        onChange={(e) => handleSettingChange('security', 'passwordExpiry', e.target.value)}
                      />
                      <Input
                        label="Max Login Attempts"
                        type="number"
                        value={settings.security.loginAttempts}
                        onChange={(e) => handleSettingChange('security', 'loginAttempts', e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Profile Settings */}
              {activeTab === 'profile' && (
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                    Profile Settings
                  </h2>
                  <div className="space-y-6">
                    <div className="flex items-center space-x-6">
                      <div className="flex-shrink-0">
                        <div className="w-20 h-20 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                          <UserIcon className="w-8 h-8 text-gray-400" />
                        </div>
                      </div>
                      <div>
                        <Button size="sm" color="primary">
                          Change Avatar
                        </Button>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                          JPG, GIF or PNG. 1MB max.
                        </p>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <Input
                        label="Full Name"
                        value={user?.name || ''}
                        readOnly
                      />
                      <Input
                        label="Email Address"
                        type="email"
                        value={user?.email || ''}
                        readOnly
                      />
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <Input
                        label="Phone Number"
                        type="tel"
                        placeholder="+****************"
                      />
                      <Input
                        label="Job Title"
                        placeholder="Administrator"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Save Button */}
              <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                <div className="flex justify-end space-x-3">
                  <Button variant="bordered">
                    Cancel
                  </Button>
                  <Button color="primary" onPress={handleSave}>
                    Save Changes
                  </Button>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>
      </div>
    </SidebarLayout>
  );
}
