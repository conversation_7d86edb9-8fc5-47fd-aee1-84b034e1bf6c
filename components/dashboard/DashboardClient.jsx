"use client";

import { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader } from '@heroui/card';
import { Button } from '@heroui/button';
import { Chip } from '@heroui/chip';
import { Divider } from '@heroui/divider';
import SidebarLayout from '@/components/layout/SidebarLayout';
import {
  Package,
  ShoppingCart,
  TrendingUp,
  Users,
  Plus,
  Eye,
  FileText,
  Settings,
  BarChart3,
  DollarSign,
  Activity
} from 'lucide-react';

/**
 * Dashboard Client Component
 *
 * Client-side dashboard with interactive functionality.
 * Receives user data from server component for immediate rendering.
 */
export default function DashboardClient({ user }) {
  const [mounted, setMounted] = useState(false);

  // Handle hydration
  useEffect(() => {
    setMounted(true);
  }, []);

  // Get user role display
  const getUserRoleDisplay = () => {
    if (!user.roles || user.roles.length === 0) return 'User';
    
    const roleMap = {
      'super_admin': 'Super Admin',
      'admin': 'Administrator',
      'manager': 'Manager',
      'staff': 'Staff',
      'viewer': 'Viewer'
    };

    return user.roles.map(role => roleMap[role] || role).join(', ');
  };

  // Get role color
  const getRoleColor = () => {
    if (!user.roles || user.roles.length === 0) return 'default';
    
    const primaryRole = user.roles[0];
    const colorMap = {
      'super_admin': 'danger',
      'admin': 'primary',
      'manager': 'secondary',
      'staff': 'success',
      'viewer': 'warning'
    };

    return colorMap[primaryRole] || 'default';
  };

  return (
    <SidebarLayout user={user}>
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Welcome back, {user.name || user.email.split('@')[0]}!
          </h2>
          <p className="text-gray-600 dark:text-gray-400 text-lg">
            Manage your inventory, orders, and system settings from this dashboard.
          </p>
        </div>

        {/* Dashboard Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {/* Quick Stats Cards */}
          <Card className="hover:shadow-lg transition-all duration-200 border-0 shadow-sm bg-white dark:bg-slate-800">
            <CardHeader className="pb-2">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Products
              </h3>
            </CardHeader>
            <CardBody className="pt-0">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-3xl font-bold text-blue-600 dark:text-blue-400">
                    0
                  </p>
                  <p className="text-sm text-gray-600 dark:text-slate-400">
                    Total products
                  </p>
                </div>
                <div className="text-blue-500 bg-blue-50 dark:bg-blue-900/20 p-3 rounded-xl">
                  <Package className="w-8 h-8" />
                </div>
              </div>
            </CardBody>
          </Card>

          <Card className="hover:shadow-lg transition-all duration-200 border-0 shadow-sm bg-white dark:bg-slate-800">
            <CardHeader className="pb-2">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Orders
              </h3>
            </CardHeader>
            <CardBody className="pt-0">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-3xl font-bold text-green-600 dark:text-green-400">
                    0
                  </p>
                  <p className="text-sm text-gray-600 dark:text-slate-400">
                    Total orders
                  </p>
                </div>
                <div className="text-green-500 bg-green-50 dark:bg-green-900/20 p-3 rounded-xl">
                  <ShoppingCart className="w-8 h-8" />
                </div>
              </div>
            </CardBody>
          </Card>

          <Card className="hover:shadow-lg transition-all duration-200 border-0 shadow-sm bg-white dark:bg-slate-800">
            <CardHeader className="pb-2">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Low Stock
              </h3>
            </CardHeader>
            <CardBody className="pt-0">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-3xl font-bold text-orange-600 dark:text-orange-400">
                    0
                  </p>
                  <p className="text-sm text-gray-600 dark:text-slate-400">
                    Items need restock
                  </p>
                </div>
                <div className="text-orange-500 bg-orange-50 dark:bg-orange-900/20 p-3 rounded-xl">
                  <TrendingUp className="w-8 h-8" />
                </div>
              </div>
            </CardBody>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card className="mb-8 border-0 shadow-sm bg-white dark:bg-slate-800">
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Quick Actions
            </h3>
          </CardHeader>
          <Divider className="dark:bg-slate-600" />
          <CardBody>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <Button
                color="primary"
                variant="flat"
                className="h-20 flex-col"
                isDisabled
              >
                <Plus className="w-6 h-6 mb-1" />
                Add Product
              </Button>
              
              <Button
                color="success"
                variant="flat"
                className="h-20 flex-col"
                isDisabled
              >
                <Eye className="w-6 h-6 mb-1" />
                View Orders
              </Button>
              
              <Button
                color="warning"
                variant="flat"
                className="h-20 flex-col"
                isDisabled
              >
                <FileText className="w-6 h-6 mb-1" />
                Manage Inventory
              </Button>
              
              <Button
                color="secondary"
                variant="flat"
                className="h-20 flex-col"
                isDisabled
              >
                <Settings className="w-6 h-6 mb-1" />
                Settings
              </Button>
            </div>
            
            <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-xl border border-blue-200 dark:border-blue-800">
              <p className="text-sm text-blue-800 dark:text-blue-200">
                <strong>🚀 Welcome to your admin dashboard!</strong> This is a prototype version with server-side authentication.
                The quick action buttons are disabled as the full features are still being developed.
              </p>
            </div>
          </CardBody>
        </Card>

        {/* User Session Info */}
        <Card className="border-0 shadow-sm bg-white dark:bg-slate-800">
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Session Information
            </h3>
          </CardHeader>
          <Divider className="dark:bg-slate-600" />
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
              <div className="space-y-1">
                <span className="font-medium text-gray-600 dark:text-slate-400">Email:</span>
                <p className="text-gray-900 dark:text-white font-medium">{user.email}</p>
              </div>
              <div className="space-y-1">
                <span className="font-medium text-gray-600 dark:text-slate-400">Role:</span>
                <div>
                  <Chip size="sm" color={getRoleColor()} variant="flat">
                    {getUserRoleDisplay()}
                  </Chip>
                </div>
              </div>
              <div className="space-y-1">
                <span className="font-medium text-gray-600 dark:text-slate-400">User ID:</span>
                <p className="text-gray-900 dark:text-white font-mono text-xs bg-gray-100 dark:bg-slate-700 px-2 py-1 rounded">{user.id}</p>
              </div>
              <div className="space-y-1">
                <span className="font-medium text-gray-600 dark:text-slate-400">Login Time:</span>
                <p className="text-gray-900 dark:text-white">
                  {mounted && user.loginAt ? new Date(user.loginAt).toLocaleString() : 'Loading...'}
                </p>
              </div>
            </div>
          </CardBody>
        </Card>
    </SidebarLayout>
  );
}
