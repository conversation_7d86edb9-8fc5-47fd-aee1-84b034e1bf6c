"use client";

import { useState, createContext, useContext } from 'react';
import { useRouter } from 'next/navigation';
import Sidebar from './Sidebar';

// Create context for sidebar state
const SidebarContext = createContext();

export const useSidebar = () => {
  const context = useContext(SidebarContext);
  if (!context) {
    throw new Error('useSidebar must be used within SidebarLayout');
  }
  return context;
};

/**
 * Sidebar Layout Component
 * 
 * Provides the main layout structure with sidebar and content area.
 * Handles responsive behavior and integrates with authentication.
 */
export default function SidebarLayout({ user, children }) {
  const [loading, setLoading] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const router = useRouter();

  // Handle logout
  const handleLogout = async () => {
    setLoading(true);
    
    try {
      const response = await fetch('/api/auth/logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      // Clear client-side state and redirect regardless of API response
      router.refresh(); // This will trigger server-side re-render
    } catch (error) {
      console.error('Logout error:', error);
      // Still redirect on error
      router.refresh();
    } finally {
      setLoading(false);
    }
  };

  const sidebarContextValue = {
    isCollapsed,
    setIsCollapsed,
  };

  return (
    <SidebarContext.Provider value={sidebarContextValue}>
      <div className="min-h-screen bg-background">
        {/* Sidebar */}
        <Sidebar
          user={user}
          onLogout={handleLogout}
          isLoading={loading}
        />

        {/* Main Content Area */}
        <div className={`
          transition-all duration-300 ease-in-out
          ${isCollapsed ? 'lg:ml-20' : 'lg:ml-72'}
          min-h-screen flex flex-col
        `}>
          {/* Mobile header spacer */}
          <div className="lg:hidden h-16" />

          {/* Main content */}
          <main className="flex-1 p-6 lg:p-8">
            <div className="max-w-7xl mx-auto">
              {children}
            </div>
          </main>
        </div>
      </div>
    </SidebarContext.Provider>
  );
}
