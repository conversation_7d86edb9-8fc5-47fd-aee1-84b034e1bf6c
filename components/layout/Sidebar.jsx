"use client";

import { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { Button } from '@heroui/button';
import { ThemeSwitch } from '@/components/theme-switch';
import { useSidebar } from './SidebarLayout';
import {
  HomeIcon,
  ShoppingBagIcon,
  ShoppingCartIcon,
  ChartBarIcon,
  CogIcon,
  UsersIcon,
  Bars3Icon,
  XMarkIcon,
  ChevronLeftIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  ArrowRightOnRectangleIcon,
  TagIcon,
  ArchiveBoxIcon,
  ClipboardDocumentListIcon,
  TruckIcon,
  CreditCardIcon,
  PresentationChartLineIcon,
  UserGroupIcon,
  BellIcon,
  ShieldCheckIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';

/**
 * Professional Sidebar Component
 * Clean, responsive, and fully functional
 */
export default function Sidebar({ user, onLogout, isLoading }) {
  const [mounted, setMounted] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [expandedItems, setExpandedItems] = useState({});
  const { isCollapsed, setIsCollapsed } = useSidebar();
  const pathname = usePathname();

  useEffect(() => {
    setMounted(true);
  }, []);

  // Auto-expand parent menu when child is active
  useEffect(() => {
    navigationItems.forEach((item) => {
      if (item.children) {
        const hasActiveChild = item.children.some(child => pathname === child.href);
        if (hasActiveChild && !expandedItems[item.name]) {
          setExpandedItems(prev => ({
            ...prev,
            [item.name]: true
          }));
        }
      }
    });
  }, [pathname]);

  const navigationItems = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: HomeIcon
    },
    {
      name: 'Products',
      href: '/products',
      icon: ShoppingBagIcon,
      children: [
        { name: 'All Products', href: '/products', icon: ArchiveBoxIcon },
        { name: 'Categories', href: '/products/categories', icon: TagIcon },
        { name: 'Inventory', href: '/products/inventory', icon: ClipboardDocumentListIcon },
      ]
    },
    {
      name: 'Orders',
      href: '/orders',
      icon: ShoppingCartIcon,
      children: [
        { name: 'All Orders', href: '/orders', icon: ClipboardDocumentListIcon },
        { name: 'Pending', href: '/orders/pending', icon: ClipboardDocumentListIcon },
        { name: 'Shipping', href: '/orders/shipping', icon: TruckIcon },
        { name: 'Completed', href: '/orders/completed', icon: ShieldCheckIcon },
      ]
    },
    {
      name: 'Analytics',
      href: '/analytics',
      icon: ChartBarIcon,
      children: [
        { name: 'Sales Report', href: '/analytics/sales', icon: PresentationChartLineIcon },
        { name: 'Revenue', href: '/analytics/revenue', icon: CreditCardIcon },
        { name: 'Customer Insights', href: '/analytics/customers', icon: UserGroupIcon },
      ]
    },
    {
      name: 'Customers',
      href: '/customers',
      icon: UsersIcon
    },
    {
      name: 'Settings',
      href: '/settings',
      icon: CogIcon,
      children: [
        { name: 'General', href: '/settings/general', icon: Cog6ToothIcon },
        { name: 'Notifications', href: '/settings/notifications', icon: BellIcon },
        { name: 'Security', href: '/settings/security', icon: ShieldCheckIcon },
      ]
    },
  ];

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  const toggleExpanded = (itemName) => {
    if (isCollapsed) return; // Don't expand when sidebar is collapsed
    setExpandedItems(prev => ({
      ...prev,
      [itemName]: !prev[itemName]
    }));
  };

  // Function to check if a menu item is active
  const isItemActive = (item) => {
    // Exact match for the main item
    if (pathname === item.href) return true;

    // Check if any child item matches the current path
    if (item.children) {
      return item.children.some(child => pathname === child.href);
    }

    return false;
  };

  // Function to check if a child item is active
  const isChildActive = (childHref) => {
    return pathname === childHref;
  };

  const getUserRoleDisplay = () => {
    if (!user?.roles || user.roles.length === 0) return 'User';
    const roleMap = {
      'super_admin': 'Super Admin',
      'admin': 'Admin',
      'manager': 'Manager',
      'staff': 'Staff',
      'viewer': 'Viewer'
    };
    return roleMap[user.roles[0]] || 'User';
  };

  return (
    <>
      {/* Mobile Overlay */}
      {isMobileMenuOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black/50 lg:hidden"
          onClick={toggleMobileMenu}
        />
      )}

      {/* Mobile Header */}
      <div className="lg:hidden fixed top-0 left-0 right-0 z-50 bg-white dark:bg-slate-900 border-b border-gray-200 dark:border-slate-700 shadow-sm">
        <div className="flex items-center justify-between h-16 px-4">
          <Button
            isIconOnly
            variant="light"
            onPress={toggleMobileMenu}
            className="text-gray-700 dark:text-gray-300"
          >
            {isMobileMenuOpen ? (
              <XMarkIcon className="h-6 w-6" />
            ) : (
              <Bars3Icon className="h-6 w-6" />
            )}
          </Button>
          <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
            Adimas Admin
          </h1>
          {mounted && <ThemeSwitch />}
        </div>
      </div>

      {/* Sidebar */}
      <div className={`
        fixed inset-y-0 left-0 z-50 transform transition-all duration-300 ease-in-out
        ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
        ${isCollapsed ? 'lg:w-20' : 'lg:w-72'}
        w-72 bg-white dark:bg-slate-900 border-r border-gray-200 dark:border-slate-700
        flex flex-col shadow-2xl lg:shadow-lg backdrop-blur-sm min-h-screen
      `}>
        
        {/* Header */}
        <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200 dark:border-slate-700">
          {!isCollapsed && (
            <h1 className="text-xl font-bold text-gray-900 dark:text-white">
              Adimas Admin
            </h1>
          )}
          <Button
            isIconOnly
            variant="light"
            size="sm"
            onPress={toggleCollapse}
            className="hidden lg:flex text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
          >
            <ChevronLeftIcon className={`h-5 w-5 transition-transform duration-300 ${isCollapsed ? 'rotate-180' : ''}`} />
          </Button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-4 py-6 space-y-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-slate-600 scrollbar-track-transparent hover:scrollbar-thumb-gray-400 dark:hover:scrollbar-thumb-slate-500">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            const isExpanded = expandedItems[item.name];
            const hasChildren = item.children && item.children.length > 0;
            const isActive = isItemActive(item);

            return (
              <div key={item.name}>
                {/* Main menu item */}
                <div
                  className={`
                    group flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 cursor-pointer
                    ${isActive
                      ? 'bg-blue-600 text-white shadow-lg'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-800 hover:text-gray-900 dark:hover:text-white'
                    }
                    ${isCollapsed ? 'justify-center' : ''}
                    relative
                  `}
                  title={isCollapsed ? item.name : ''}
                  onClick={(e) => {
                    if (hasChildren && !isCollapsed) {
                      e.preventDefault();
                      toggleExpanded(item.name);
                    } else {
                      window.location.href = item.href;
                    }
                  }}
                >
                  <Icon className={`h-6 w-6 ${isCollapsed ? '' : 'mr-3'} flex-shrink-0`} />
                  {!isCollapsed && (
                    <>
                      <span className="flex-1">{item.name}</span>
                      {hasChildren && (
                        <div className="ml-2">
                          {isExpanded ? (
                            <ChevronDownIcon className="h-4 w-4" />
                          ) : (
                            <ChevronRightIcon className="h-4 w-4" />
                          )}
                        </div>
                      )}
                    </>
                  )}

                  {/* Tooltip for collapsed state */}
                  {isCollapsed && (
                    <div className="absolute left-full ml-3 px-3 py-2 bg-gray-900 dark:bg-slate-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50 shadow-lg">
                      {item.name}
                    </div>
                  )}
                </div>

                {/* Submenu items */}
                {hasChildren && isExpanded && !isCollapsed && (
                  <div className="ml-6 mt-1 space-y-1">
                    {item.children.map((child) => {
                      const ChildIcon = child.icon;
                      const isChildActiveState = isChildActive(child.href);
                      return (
                        <a
                          key={child.name}
                          href={child.href}
                          className={`
                            group flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200
                            ${isChildActiveState
                              ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
                              : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-800 hover:text-gray-900 dark:hover:text-white'
                            }
                          `}
                        >
                          <ChildIcon className="h-5 w-5 mr-3 flex-shrink-0" />
                          <span>{child.name}</span>
                        </a>
                      );
                    })}
                  </div>
                )}
              </div>
            );
          })}
        </nav>

        {/* Theme Switch */}
        <div className="px-4 py-4 border-t border-gray-200 dark:border-slate-700">
          <div className={`flex items-center ${isCollapsed ? 'justify-center' : 'justify-between'}`}>
            {!isCollapsed && (
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Theme
              </span>
            )}
            {mounted && <ThemeSwitch />}
          </div>
        </div>

        {/* User Section */}
        <div className="px-4 py-4 border-t border-gray-200 dark:border-slate-700 bg-gray-50 dark:bg-slate-800/50">
          {!isCollapsed && (
            <div className="mb-4 p-4 bg-white dark:bg-slate-800 rounded-lg border border-gray-200 dark:border-slate-600 shadow-sm">
              <p className="text-sm font-semibold text-gray-900 dark:text-white truncate">
                {user?.name || user?.email}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {getUserRoleDisplay()}
              </p>
            </div>
          )}
          
          <Button
            color="danger"
            variant={isCollapsed ? "flat" : "solid"}
            size="sm"
            onPress={onLogout}
            isLoading={isLoading}
            className={`w-full ${isCollapsed ? 'px-0' : ''} font-medium`}
            startContent={!isCollapsed && <ArrowRightOnRectangleIcon className="h-4 w-4" />}
          >
            {isCollapsed ? <ArrowRightOnRectangleIcon className="h-5 w-5" /> : (isLoading ? 'Logging out...' : 'Logout')}
          </Button>
        </div>
      </div>
    </>
  );
}
