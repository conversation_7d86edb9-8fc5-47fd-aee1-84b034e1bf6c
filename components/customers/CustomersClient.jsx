"use client";

import { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader } from '@heroui/card';
import { Button } from '@heroui/button';
import { Input } from '@heroui/input';
import { Chip } from '@heroui/chip';
import { Table, TableHeader, TableColumn, TableBody, TableRow, TableCell } from '@heroui/table';
import { Avatar } from '@heroui/avatar';
import SidebarLayout from '@/components/layout/SidebarLayout';
import { 
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon,
  PencilIcon,
  EnvelopeIcon
} from '@heroicons/react/24/outline';

/**
 * Customers Client Component
 *
 * Client-side customers management with interactive functionality.
 */
export default function CustomersClient({ user }) {
  const [mounted, setMounted] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [customers, setCustomers] = useState([]);
  const [loading, setLoading] = useState(false);

  // Handle hydration
  useEffect(() => {
    setMounted(true);
    // TODO: Fetch customers from API
    loadMockCustomers();
  }, []);

  const loadMockCustomers = () => {
    // Mock data for demonstration
    const mockCustomers = [
      {
        id: 1,
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+****************',
        totalOrders: 12,
        totalSpent: 1299.99,
        status: 'active',
        joinDate: '2023-08-15',
        lastOrder: '2024-01-10',
        avatar: null
      },
      {
        id: 2,
        name: 'Jane Smith',
        email: '<EMAIL>',
        phone: '+****************',
        totalOrders: 8,
        totalSpent: 649.99,
        status: 'active',
        joinDate: '2023-09-22',
        lastOrder: '2024-01-08',
        avatar: null
      },
      {
        id: 3,
        name: 'Bob Johnson',
        email: '<EMAIL>',
        phone: '+****************',
        totalOrders: 25,
        totalSpent: 2899.99,
        status: 'vip',
        joinDate: '2023-06-10',
        lastOrder: '2024-01-12',
        avatar: null
      },
      {
        id: 4,
        name: 'Alice Brown',
        email: '<EMAIL>',
        phone: '+****************',
        totalOrders: 3,
        totalSpent: 189.99,
        status: 'inactive',
        joinDate: '2023-11-05',
        lastOrder: '2023-12-20',
        avatar: null
      }
    ];
    setCustomers(mockCustomers);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'success';
      case 'vip': return 'secondary';
      case 'inactive': return 'warning';
      case 'blocked': return 'danger';
      default: return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'active': return 'Active';
      case 'vip': return 'VIP';
      case 'inactive': return 'Inactive';
      case 'blocked': return 'Blocked';
      default: return 'Unknown';
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const filteredCustomers = customers.filter(customer =>
    customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    customer.phone.includes(searchTerm)
  );

  return (
    <SidebarLayout user={user}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Customers
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Manage your customer relationships and data
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <Button
            color="primary"
            startContent={<PlusIcon className="h-5 w-5" />}
            className="font-medium"
          >
            Add Customer
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card className="shadow-sm border-0">
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Customers</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{customers.length}</p>
              </div>
              <div className="text-blue-500 bg-blue-50 dark:bg-blue-900/20 p-3 rounded-xl">
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
                </svg>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="shadow-sm border-0">
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {customers.filter(c => c.status === 'active').length}
                </p>
              </div>
              <div className="text-green-500 bg-green-50 dark:bg-green-900/20 p-3 rounded-xl">
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="shadow-sm border-0">
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">VIP</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {customers.filter(c => c.status === 'vip').length}
                </p>
              </div>
              <div className="text-purple-500 bg-purple-50 dark:bg-purple-900/20 p-3 rounded-xl">
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="shadow-sm border-0">
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Revenue</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {formatCurrency(customers.reduce((sum, customer) => sum + customer.totalSpent, 0))}
                </p>
              </div>
              <div className="text-green-500 bg-green-50 dark:bg-green-900/20 p-3 rounded-xl">
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card className="mb-6 shadow-sm border-0">
        <CardBody className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search customers by name, email, or phone..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                startContent={<MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />}
                className="w-full"
              />
            </div>
            <Button
              variant="bordered"
              startContent={<FunnelIcon className="h-5 w-5" />}
              className="sm:w-auto"
            >
              Filters
            </Button>
          </div>
        </CardBody>
      </Card>

      {/* Customers Table */}
      <Card className="shadow-sm border-0">
        <CardBody className="p-0">
          <Table aria-label="Customers table">
            <TableHeader>
              <TableColumn>CUSTOMER</TableColumn>
              <TableColumn>CONTACT</TableColumn>
              <TableColumn>ORDERS</TableColumn>
              <TableColumn>TOTAL SPENT</TableColumn>
              <TableColumn>STATUS</TableColumn>
              <TableColumn>LAST ORDER</TableColumn>
              <TableColumn>ACTIONS</TableColumn>
            </TableHeader>
            <TableBody>
              {filteredCustomers.map((customer) => (
                <TableRow key={customer.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar
                        src={customer.avatar}
                        name={customer.name}
                        size="sm"
                        className="flex-shrink-0"
                      />
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">{customer.name}</p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          Joined {formatDate(customer.joinDate)}
                        </p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <p className="text-sm text-gray-900 dark:text-white">{customer.email}</p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">{customer.phone}</p>
                    </div>
                  </TableCell>
                  <TableCell className="font-medium">{customer.totalOrders}</TableCell>
                  <TableCell className="font-medium">{formatCurrency(customer.totalSpent)}</TableCell>
                  <TableCell>
                    <Chip
                      size="sm"
                      color={getStatusColor(customer.status)}
                      variant="flat"
                    >
                      {getStatusText(customer.status)}
                    </Chip>
                  </TableCell>
                  <TableCell>{formatDate(customer.lastOrder)}</TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="flat"
                        color="primary"
                        isIconOnly
                      >
                        <EyeIcon className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="flat"
                        color="warning"
                        isIconOnly
                      >
                        <PencilIcon className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="flat"
                        color="secondary"
                        isIconOnly
                      >
                        <EnvelopeIcon className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardBody>
      </Card>

      {/* Empty State */}
      {filteredCustomers.length === 0 && (
        <Card className="mt-8 shadow-sm border-0">
          <CardBody className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No customers found
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {searchTerm ? 'Try adjusting your search terms.' : 'Get started by adding your first customer.'}
            </p>
            {!searchTerm && (
              <Button
                color="primary"
                startContent={<PlusIcon className="h-5 w-5" />}
              >
                Add Your First Customer
              </Button>
            )}
          </CardBody>
        </Card>
      )}
    </SidebarLayout>
  );
}
